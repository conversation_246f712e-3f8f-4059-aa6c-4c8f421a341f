/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivityD android.app.Application+androidx.camera.core.CameraXConfig.Provider androidx.room.RoomDatabase android.os.Parcelable. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem* )java.lang.Thread.UncaughtExceptionHandler coil.fetch.Fetcher coil.fetch.Fetcher.Factory$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem$ #androidx.lifecycle.AndroidViewModel. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem. -com.ml.tomatoscan.ui.navigation.BottomNavItem