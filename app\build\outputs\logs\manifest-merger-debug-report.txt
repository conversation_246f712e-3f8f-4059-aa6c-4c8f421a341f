-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:35:9-43:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:39:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:37:13-60
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:38:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:36:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:2:1-46:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac82bfe1b55c5297ccfca56a4d83e86\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d712c985431b5e7aef45e80b23bf8468\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\597583f4ee95129bbeb222cbff97d591\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec4069bfd1d92a4fc914ce9dfdebd91\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c06d3bd30ccfad989e4fd516d0a3ec\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\827848161c01d8e105936428022348b0\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt:coil-compose:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae5efa0d1ca90b064a01eaa0ceeb5871\transformed\coil-compose-2.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41bc30faf334d9c05d71dba3bd9f894d\transformed\coil-compose-base-2.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa56867d511c0ea4697e72a08436c4eb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d575a9e22c1fd6faca44b606d2371c9b\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\786457ca65879c22acde8c7aed3ef6bc\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e558f82f28977c61067cff1ffaa910d\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aef2b099c308afe7b74c31a0cefc610e\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1ee23f00ce5f297c3ad2f5b3914e6f4\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0383d0ba5febfb1ed85dd953ea931fa2\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a6017f1e74a450a02aa60bf47904178\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\27f908830ecde1195c7d1468bed0058f\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\213b5a8766ac0a3456c4182c3ecacd0e\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b3ddd318fe9e0d9d569121c92a10ab1\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2540ec706e630cd63a630498acd97ae8\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4b6e959e5a02db3dbdaef3e0c91925\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6bb74210ae1d7265ae291f685894631\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e4bb758916387194c734656d34188c7\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.camera:camera-video:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4681e519ba4151564b1dfb204fd3c7e1\transformed\camera-video-1.3.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\29703a8d71786ba318cd640639b080bf\transformed\camera-view-1.3.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7661a5fd0d3df1d864232356b350fef\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d64d33bbc96798f145a33ff99d8a6ee\transformed\camera-core-1.3.4\AndroidManifest.xml:17:1-36:12
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab6cbaf7bf230b6b6d5af555fdbb71dd\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-analytics:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\312048215a751909e931298e79c6dc78\transformed\firebase-analytics-22.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c8958e45256498062a2c4e8f058e38a\transformed\play-services-measurement-sdk-22.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5468497be68ad4ba67415839697dae57\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7987a73f3a9893c2d088a2bfa03c080e\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbbb2320ee047593097f2dffcb5f1ff5\transformed\integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c852522289c6f5864f126fdbf98de4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ea7dcc1f0872ed8b54d202440c91696\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1281294b4af427fde462f75c68044c74\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.ai.client.generativeai:generativeai:0.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a245028d0847c2b75ac8ce7cff56cd0\transformed\generativeai-0.3.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49345e8c7426a4c953a1488c4c6ad5cf\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ai.client.generativeai:common:0.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ca9d709d7cd11c62ac2d6c4cadda3be\transformed\common-0.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.coil-kt:coil:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4c735f418a264b77b9ea76b03c9b5c\transformed\coil-2.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ecdc65225691c83e9d9f77d6b8cb8da\transformed\coil-base-2.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2f13ea85cbf3127b074729e3268e91a\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f79a2239b8b09cce6f26afdda6c19fe3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\69bb14c86ef59087665819ebd00c55e3\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\42c95fee7119748de6e9f01303bde1b1\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3ae901071214bde197020db9c1aa8c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d5cb58bacb9a0b03b60afe3e3ab7d36\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4073bc2e6f6d89e3ea8d0133509ac240\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5192495d022e7fe6e8d0e58d9587bb6\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3dc09306329bb7620905ed7e9c509954\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\03e3b34903ecd53c310151ef34c7f931\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de23d73a7bb4fb1f2b936d19aece5df3\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d8e9c69c425ecb75a2a0c2a569b84e\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e196c2c492c26804c92b3236aa37953\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7eb931d2788947f0022d909f1391480f\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43be77cc67f1a18184d6e6ba33aea269\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\31688599dc18510b240530df420f5aab\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\625e4c9cf8ba3c5758556d1ec9d33907\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7819191283cfc1031a83fe3d2067df0a\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b4ef6388e6bf051e10f38bc051bbf5\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e191e1daaf4b0a2ac63233cc628e47a\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7c754d9a78f61a7624fcd3ec6e6a2dc\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc848fccd3de6642cb95f7b069933c4\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a991095dfecade19cf696c499f6dba0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97cc24afe80e15fa70bd2926f3277073\transformed\play-services-measurement-base-22.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6de7ab7305ac45563f32ba8199d3e304\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d934593cab4de80219eb571e0fe7548b\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94c00df77771e733569544fac7b91a79\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb348357f2111471995f55e778811ec3\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58b8246fccf21adf7dfe04412852d9fd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6ddc95f468115f6483ef59854459986\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d358a09a55d0239f5aebb74a13b63ef5\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6868a2750529401637efa3ab01f4fce1\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4907ac51db62be9a38a63c4fc72f81\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9eb6595b88cb5a94e9e9a83ebd14d7f\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ba4643e303b0d9942cafa9695469c1b\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\31499c9a3909b7f1fbd71768fed90194\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0427cae474247921015aeaee892d4db\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\212943b7a9919b93dbc2037eeaaa9d94\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\752288bc1bab23b3c0ad606907958a34\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\425f03000299fb0b44cf96ca655409bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15565a1ca2e28b4e4602cc9ced167200\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6d3666cc15bbceba30fb88166a9dec3\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\398c2a74b03599659b5ead5e2fab5e05\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b862771fe750953b0a80430122a3472f\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf128f550c37e8a4ce2b4b3a5f733294\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\50a090a63d4aa963ae61775da09b5544\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0a2c29bba24857d7c570746c5786e70\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e577f664435d2cdd8c8fcbe639cd6e5\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23d4a51cbab18d31afef8ebe857d0764\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03f9815192a0531dff3e4fa5ec4bd84b\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ab00b2ac9f2976ce906d3e02b644e9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f04e197689bb26cdcc3b07cde404826\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8fa4fe622703a2fec2bd0274e83f860\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0973d0a1688a5d0bd27a300a8660ae3f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\362e2d338c5c7faaf1e785711c2ac636\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9877fbe30172a287478ca9a87a512205\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\515ac6bf9d058a93d24f8dcd1ab51b86\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bfda9d4cdeb081d06d52af3b254f283\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f12e361727353463b8ae2674b3fd871\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\815fcbe92cd40c74aa601a86cff0c471\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b7b0289412cf623a14a5d5d63de3e2\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\66a9c4073f62fbaaa877f36d1dd4c062\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27f2eb18306dfd9901fd5340fa832c4a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30dac0b148efc3edb5ca4dc4adf58fa3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b57e06e7fd9d1b77c2e118f15d80896\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1866c99811d42cbbcf97d48d9fc78d6f\transformed\grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5552a37286486fecc2556427c131eeb5\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:5:5-85
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:5:58-82
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:5:19-57
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:9:22-77
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:12:5-44:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:12:5-44:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4b6e959e5a02db3dbdaef3e0c91925\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4b6e959e5a02db3dbdaef3e0c91925\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d64d33bbc96798f145a33ff99d8a6ee\transformed\camera-core-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d64d33bbc96798f145a33ff99d8a6ee\transformed\camera-core-1.3.4\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-analytics:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\312048215a751909e931298e79c6dc78\transformed\firebase-analytics-22.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\312048215a751909e931298e79c6dc78\transformed\firebase-analytics-22.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c8958e45256498062a2c4e8f058e38a\transformed\play-services-measurement-sdk-22.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c8958e45256498062a2c4e8f058e38a\transformed\play-services-measurement-sdk-22.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbbb2320ee047593097f2dffcb5f1ff5\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbbb2320ee047593097f2dffcb5f1ff5\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c852522289c6f5864f126fdbf98de4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c852522289c6f5864f126fdbf98de4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ea7dcc1f0872ed8b54d202440c91696\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ea7dcc1f0872ed8b54d202440c91696\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1281294b4af427fde462f75c68044c74\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1281294b4af427fde462f75c68044c74\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2f13ea85cbf3127b074729e3268e91a\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2f13ea85cbf3127b074729e3268e91a\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b4ef6388e6bf051e10f38bc051bbf5\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b4ef6388e6bf051e10f38bc051bbf5\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc848fccd3de6642cb95f7b069933c4\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc848fccd3de6642cb95f7b069933c4\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a991095dfecade19cf696c499f6dba0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a991095dfecade19cf696c499f6dba0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97cc24afe80e15fa70bd2926f3277073\transformed\play-services-measurement-base-22.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97cc24afe80e15fa70bd2926f3277073\transformed\play-services-measurement-base-22.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6de7ab7305ac45563f32ba8199d3e304\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6de7ab7305ac45563f32ba8199d3e304\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d934593cab4de80219eb571e0fe7548b\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d934593cab4de80219eb571e0fe7548b\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0973d0a1688a5d0bd27a300a8660ae3f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0973d0a1688a5d0bd27a300a8660ae3f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\815fcbe92cd40c74aa601a86cff0c471\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\815fcbe92cd40c74aa601a86cff0c471\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:20:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:18:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:19:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:22:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:17:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:14:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:21:9-48
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:15:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:13:9-46
activity#com.ml.tomatoscan.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:23:9-33:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:26:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:27:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:24:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:28:13-32:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:31:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:31:27-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:40:13-42:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:42:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:41:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac82bfe1b55c5297ccfca56a4d83e86\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bac82bfe1b55c5297ccfca56a4d83e86\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d712c985431b5e7aef45e80b23bf8468\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\d712c985431b5e7aef45e80b23bf8468\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\597583f4ee95129bbeb222cbff97d591\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\597583f4ee95129bbeb222cbff97d591\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec4069bfd1d92a4fc914ce9dfdebd91\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ec4069bfd1d92a4fc914ce9dfdebd91\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c06d3bd30ccfad989e4fd516d0a3ec\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c06d3bd30ccfad989e4fd516d0a3ec\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\827848161c01d8e105936428022348b0\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\827848161c01d8e105936428022348b0\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil-compose:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae5efa0d1ca90b064a01eaa0ceeb5871\transformed\coil-compose-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae5efa0d1ca90b064a01eaa0ceeb5871\transformed\coil-compose-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41bc30faf334d9c05d71dba3bd9f894d\transformed\coil-compose-base-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41bc30faf334d9c05d71dba3bd9f894d\transformed\coil-compose-base-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa56867d511c0ea4697e72a08436c4eb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa56867d511c0ea4697e72a08436c4eb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d575a9e22c1fd6faca44b606d2371c9b\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d575a9e22c1fd6faca44b606d2371c9b\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\786457ca65879c22acde8c7aed3ef6bc\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\786457ca65879c22acde8c7aed3ef6bc\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e558f82f28977c61067cff1ffaa910d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e558f82f28977c61067cff1ffaa910d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aef2b099c308afe7b74c31a0cefc610e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aef2b099c308afe7b74c31a0cefc610e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1ee23f00ce5f297c3ad2f5b3914e6f4\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1ee23f00ce5f297c3ad2f5b3914e6f4\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0383d0ba5febfb1ed85dd953ea931fa2\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0383d0ba5febfb1ed85dd953ea931fa2\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a6017f1e74a450a02aa60bf47904178\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a6017f1e74a450a02aa60bf47904178\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\27f908830ecde1195c7d1468bed0058f\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\27f908830ecde1195c7d1468bed0058f\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\213b5a8766ac0a3456c4182c3ecacd0e\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\213b5a8766ac0a3456c4182c3ecacd0e\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b3ddd318fe9e0d9d569121c92a10ab1\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b3ddd318fe9e0d9d569121c92a10ab1\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2540ec706e630cd63a630498acd97ae8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2540ec706e630cd63a630498acd97ae8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4b6e959e5a02db3dbdaef3e0c91925\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4b6e959e5a02db3dbdaef3e0c91925\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6bb74210ae1d7265ae291f685894631\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6bb74210ae1d7265ae291f685894631\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e4bb758916387194c734656d34188c7\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e4bb758916387194c734656d34188c7\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-video:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4681e519ba4151564b1dfb204fd3c7e1\transformed\camera-video-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\4681e519ba4151564b1dfb204fd3c7e1\transformed\camera-video-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\29703a8d71786ba318cd640639b080bf\transformed\camera-view-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\29703a8d71786ba318cd640639b080bf\transformed\camera-view-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7661a5fd0d3df1d864232356b350fef\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7661a5fd0d3df1d864232356b350fef\transformed\camera-lifecycle-1.3.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d64d33bbc96798f145a33ff99d8a6ee\transformed\camera-core-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d64d33bbc96798f145a33ff99d8a6ee\transformed\camera-core-1.3.4\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab6cbaf7bf230b6b6d5af555fdbb71dd\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab6cbaf7bf230b6b6d5af555fdbb71dd\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-analytics:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\312048215a751909e931298e79c6dc78\transformed\firebase-analytics-22.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\312048215a751909e931298e79c6dc78\transformed\firebase-analytics-22.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c8958e45256498062a2c4e8f058e38a\transformed\play-services-measurement-sdk-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c8958e45256498062a2c4e8f058e38a\transformed\play-services-measurement-sdk-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5468497be68ad4ba67415839697dae57\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\5468497be68ad4ba67415839697dae57\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7987a73f3a9893c2d088a2bfa03c080e\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7987a73f3a9893c2d088a2bfa03c080e\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbbb2320ee047593097f2dffcb5f1ff5\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbbb2320ee047593097f2dffcb5f1ff5\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c852522289c6f5864f126fdbf98de4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c852522289c6f5864f126fdbf98de4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ea7dcc1f0872ed8b54d202440c91696\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ea7dcc1f0872ed8b54d202440c91696\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1281294b4af427fde462f75c68044c74\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1281294b4af427fde462f75c68044c74\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.ai.client.generativeai:generativeai:0.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a245028d0847c2b75ac8ce7cff56cd0\transformed\generativeai-0.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:generativeai:0.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a245028d0847c2b75ac8ce7cff56cd0\transformed\generativeai-0.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49345e8c7426a4c953a1488c4c6ad5cf\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49345e8c7426a4c953a1488c4c6ad5cf\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:common:0.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ca9d709d7cd11c62ac2d6c4cadda3be\transformed\common-0.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:common:0.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ca9d709d7cd11c62ac2d6c4cadda3be\transformed\common-0.2.0\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4c735f418a264b77b9ea76b03c9b5c\transformed\coil-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4c735f418a264b77b9ea76b03c9b5c\transformed\coil-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ecdc65225691c83e9d9f77d6b8cb8da\transformed\coil-base-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ecdc65225691c83e9d9f77d6b8cb8da\transformed\coil-base-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2f13ea85cbf3127b074729e3268e91a\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2f13ea85cbf3127b074729e3268e91a\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f79a2239b8b09cce6f26afdda6c19fe3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f79a2239b8b09cce6f26afdda6c19fe3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\69bb14c86ef59087665819ebd00c55e3\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\69bb14c86ef59087665819ebd00c55e3\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\42c95fee7119748de6e9f01303bde1b1\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\42c95fee7119748de6e9f01303bde1b1\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3ae901071214bde197020db9c1aa8c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3ae901071214bde197020db9c1aa8c\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d5cb58bacb9a0b03b60afe3e3ab7d36\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d5cb58bacb9a0b03b60afe3e3ab7d36\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4073bc2e6f6d89e3ea8d0133509ac240\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\4073bc2e6f6d89e3ea8d0133509ac240\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5192495d022e7fe6e8d0e58d9587bb6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d5192495d022e7fe6e8d0e58d9587bb6\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3dc09306329bb7620905ed7e9c509954\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3dc09306329bb7620905ed7e9c509954\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\03e3b34903ecd53c310151ef34c7f931\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\03e3b34903ecd53c310151ef34c7f931\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de23d73a7bb4fb1f2b936d19aece5df3\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\de23d73a7bb4fb1f2b936d19aece5df3\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d8e9c69c425ecb75a2a0c2a569b84e\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d8e9c69c425ecb75a2a0c2a569b84e\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e196c2c492c26804c92b3236aa37953\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e196c2c492c26804c92b3236aa37953\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7eb931d2788947f0022d909f1391480f\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7eb931d2788947f0022d909f1391480f\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43be77cc67f1a18184d6e6ba33aea269\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43be77cc67f1a18184d6e6ba33aea269\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\31688599dc18510b240530df420f5aab\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\31688599dc18510b240530df420f5aab\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\625e4c9cf8ba3c5758556d1ec9d33907\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\625e4c9cf8ba3c5758556d1ec9d33907\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7819191283cfc1031a83fe3d2067df0a\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7819191283cfc1031a83fe3d2067df0a\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b4ef6388e6bf051e10f38bc051bbf5\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b4ef6388e6bf051e10f38bc051bbf5\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e191e1daaf4b0a2ac63233cc628e47a\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e191e1daaf4b0a2ac63233cc628e47a\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7c754d9a78f61a7624fcd3ec6e6a2dc\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7c754d9a78f61a7624fcd3ec6e6a2dc\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc848fccd3de6642cb95f7b069933c4\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0dc848fccd3de6642cb95f7b069933c4\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a991095dfecade19cf696c499f6dba0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a991095dfecade19cf696c499f6dba0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97cc24afe80e15fa70bd2926f3277073\transformed\play-services-measurement-base-22.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97cc24afe80e15fa70bd2926f3277073\transformed\play-services-measurement-base-22.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6de7ab7305ac45563f32ba8199d3e304\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6de7ab7305ac45563f32ba8199d3e304\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d934593cab4de80219eb571e0fe7548b\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d934593cab4de80219eb571e0fe7548b\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94c00df77771e733569544fac7b91a79\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\94c00df77771e733569544fac7b91a79\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb348357f2111471995f55e778811ec3\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb348357f2111471995f55e778811ec3\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58b8246fccf21adf7dfe04412852d9fd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58b8246fccf21adf7dfe04412852d9fd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6ddc95f468115f6483ef59854459986\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6ddc95f468115f6483ef59854459986\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d358a09a55d0239f5aebb74a13b63ef5\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d358a09a55d0239f5aebb74a13b63ef5\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6868a2750529401637efa3ab01f4fce1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6868a2750529401637efa3ab01f4fce1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4907ac51db62be9a38a63c4fc72f81\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4907ac51db62be9a38a63c4fc72f81\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9eb6595b88cb5a94e9e9a83ebd14d7f\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9eb6595b88cb5a94e9e9a83ebd14d7f\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ba4643e303b0d9942cafa9695469c1b\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ba4643e303b0d9942cafa9695469c1b\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\31499c9a3909b7f1fbd71768fed90194\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\31499c9a3909b7f1fbd71768fed90194\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0427cae474247921015aeaee892d4db\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0427cae474247921015aeaee892d4db\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\212943b7a9919b93dbc2037eeaaa9d94\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\212943b7a9919b93dbc2037eeaaa9d94\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\752288bc1bab23b3c0ad606907958a34\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\752288bc1bab23b3c0ad606907958a34\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\425f03000299fb0b44cf96ca655409bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\425f03000299fb0b44cf96ca655409bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15565a1ca2e28b4e4602cc9ced167200\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\15565a1ca2e28b4e4602cc9ced167200\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6d3666cc15bbceba30fb88166a9dec3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6d3666cc15bbceba30fb88166a9dec3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\398c2a74b03599659b5ead5e2fab5e05\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\398c2a74b03599659b5ead5e2fab5e05\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b862771fe750953b0a80430122a3472f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b862771fe750953b0a80430122a3472f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf128f550c37e8a4ce2b4b3a5f733294\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf128f550c37e8a4ce2b4b3a5f733294\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\50a090a63d4aa963ae61775da09b5544\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\50a090a63d4aa963ae61775da09b5544\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0a2c29bba24857d7c570746c5786e70\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0a2c29bba24857d7c570746c5786e70\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e577f664435d2cdd8c8fcbe639cd6e5\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e577f664435d2cdd8c8fcbe639cd6e5\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23d4a51cbab18d31afef8ebe857d0764\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23d4a51cbab18d31afef8ebe857d0764\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03f9815192a0531dff3e4fa5ec4bd84b\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\03f9815192a0531dff3e4fa5ec4bd84b\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ab00b2ac9f2976ce906d3e02b644e9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\23ab00b2ac9f2976ce906d3e02b644e9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f04e197689bb26cdcc3b07cde404826\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f04e197689bb26cdcc3b07cde404826\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8fa4fe622703a2fec2bd0274e83f860\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8fa4fe622703a2fec2bd0274e83f860\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0973d0a1688a5d0bd27a300a8660ae3f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0973d0a1688a5d0bd27a300a8660ae3f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\362e2d338c5c7faaf1e785711c2ac636\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\362e2d338c5c7faaf1e785711c2ac636\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9877fbe30172a287478ca9a87a512205\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9877fbe30172a287478ca9a87a512205\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\515ac6bf9d058a93d24f8dcd1ab51b86\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\515ac6bf9d058a93d24f8dcd1ab51b86\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bfda9d4cdeb081d06d52af3b254f283\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bfda9d4cdeb081d06d52af3b254f283\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f12e361727353463b8ae2674b3fd871\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f12e361727353463b8ae2674b3fd871\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\815fcbe92cd40c74aa601a86cff0c471\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\815fcbe92cd40c74aa601a86cff0c471\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b7b0289412cf623a14a5d5d63de3e2\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\85b7b0289412cf623a14a5d5d63de3e2\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\66a9c4073f62fbaaa877f36d1dd4c062\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\66a9c4073f62fbaaa877f36d1dd4c062\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27f2eb18306dfd9901fd5340fa832c4a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27f2eb18306dfd9901fd5340fa832c4a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30dac0b148efc3edb5ca4dc4adf58fa3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30dac0b148efc3edb5ca4dc4adf58fa3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b57e06e7fd9d1b77c2e118f15d80896\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b57e06e7fd9d1b77c2e118f15d80896\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1866c99811d42cbbcf97d48d9fc78d6f\transformed\grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1866c99811d42cbbcf97d48d9fc78d6f\transformed\grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5552a37286486fecc2556427c131eeb5\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5552a37286486fecc2556427c131eeb5\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4b6e959e5a02db3dbdaef3e0c91925\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4b6e959e5a02db3dbdaef3e0c91925\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a4b6e959e5a02db3dbdaef3e0c91925\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d64d33bbc96798f145a33ff99d8a6ee\transformed\camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d64d33bbc96798f145a33ff99d8a6ee\transformed\camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9dfcc0cedb9d723d153595ce0357f93\transformed\camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
uses-permission#android.permission.INTERNET
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.ai.client.generativeai:generativeai:0.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a245028d0847c2b75ac8ce7cff56cd0\transformed\generativeai-0.3.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.ai.client.generativeai:generativeai:0.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a245028d0847c2b75ac8ce7cff56cd0\transformed\generativeai-0.3.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.ai.client.generativeai:common:0.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ca9d709d7cd11c62ac2d6c4cadda3be\transformed\common-0.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.ai.client.generativeai:common:0.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ca9d709d7cd11c62ac2d6c4cadda3be\transformed\common-0.2.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1866c99811d42cbbcf97d48d9fc78d6f\transformed\grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1866c99811d42cbbcf97d48d9fc78d6f\transformed\grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f204db800e675439366b286670348ef\transformed\firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\444f34f2f0827fda1c4618289fdaeda5\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:21:17-111
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1afa92abbc74df3d7b1ef025b0373836\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7418d99018d6d278efbe7e033aae0c\transformed\play-services-measurement-impl-22.0.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a991095dfecade19cf696c499f6dba0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a991095dfecade19cf696c499f6dba0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8abadc3ccf52c6b719cb24f1f93eb3a6\transformed\play-services-measurement-sdk-api-22.0.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d23b23e1df4d04262ee96b7649a72284\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:38:17-139
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacb3293e83182893e1276d9fbc1ffe\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6878fc4c8395cf99523a7469393450b7\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a0b11bc89d87e27ce1f43befded107e8\transformed\firebase-storage-21.0.0\AndroidManifest.xml:34:17-107
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5000e9f87679b3b5d76b10562daa325e\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5202d6f8fe7c57475e9cdcc4a0d8e9ef\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273b3fceef9c1075f8e8bd733a864fe6\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a07a5671f5585299b08c49f2f914859\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10c1dcbc493e6218b6c9be1f331b8358\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b42f75b2618132577c05197f8188cf8\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e33c8e88ddf3587b7d540033450542b0\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\815fcbe92cd40c74aa601a86cff0c471\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\815fcbe92cd40c74aa601a86cff0c471\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33e7d55074004f01006d18b0bb7946\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd91ee51a23c5d3b117149799bd14e92\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\501b230c67bbf5b5cc8f17f3c0f62f76\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b4ef6388e6bf051e10f38bc051bbf5\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b4ef6388e6bf051e10f38bc051bbf5\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\49b4ef6388e6bf051e10f38bc051bbf5\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d934593cab4de80219eb571e0fe7548b\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d934593cab4de80219eb571e0fe7548b\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d934593cab4de80219eb571e0fe7548b\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.ml.tomatoscan.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.ml.tomatoscan.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9cf21ceb9549d3cbcfc1e34a7f2650da\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\041840b121efb3f0b1ca81a46045a124\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
