package com.ml.tomatoscan.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u0000 \u00122\u00020\u0001:\u0001\u0012B\u0005\u00a2\u0006\u0002\u0010\u0002J\"\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00042\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0010\u0010\f\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u0005H\u0002J\u0018\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0002\u00a8\u0006\u0013"}, d2 = {"Lcom/ml/tomatoscan/data/GeminiApi;", "", "()V", "analyzeImage", "Lkotlin/Pair;", "", "", "bitmap", "Landroid/graphics/Bitmap;", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "analyzeTomatoLeaf", "Lcom/ml/tomatoscan/data/TomatoAnalysisResult;", "cleanJsonResponse", "response", "parseJsonArray", "", "jsonArray", "Lorg/json/JSONArray;", "Companion", "app_debug"})
public final class GeminiApi {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_KEY = "AIzaSyBD15s-m0ClELhAR7XbbVPRkSFlQzcu_fQ";
    @org.jetbrains.annotations.NotNull()
    private static final kotlin.Lazy<?> generativeModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.ml.tomatoscan.data.GeminiApi.Companion Companion = null;
    
    public GeminiApi() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object analyzeTomatoLeaf(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.ml.tomatoscan.data.TomatoAnalysisResult> $completion) {
        return null;
    }
    
    private final java.lang.String cleanJsonResponse(java.lang.String response) {
        return null;
    }
    
    private final java.util.List<java.lang.String> parseJsonArray(org.json.JSONArray jsonArray) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object analyzeImage(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Pair<java.lang.String, java.lang.Float>> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0005\u001a\u0004\u0018\u00010\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u000b"}, d2 = {"Lcom/ml/tomatoscan/data/GeminiApi$Companion;", "", "()V", "API_KEY", "", "generativeModel", "Lcom/google/ai/client/generativeai/GenerativeModel;", "getGenerativeModel", "()Lcom/google/ai/client/generativeai/GenerativeModel;", "generativeModel$delegate", "Lkotlin/Lazy;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        private final com.google.ai.client.generativeai.GenerativeModel getGenerativeModel() {
            return null;
        }
    }
}