package com.ml.tomatoscan.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0012\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000b\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010\rJ\u000e\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\u0082@\u00a2\u0006\u0002\u0010\u0014J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u001a\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010\u001aH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0012\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u001d0\u001cJ\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u001f2\u0006\u0010\u0016\u001a\u00020\u0017J.\u0010 \u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00172\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\"2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u001fH\u0086@\u00a2\u0006\u0002\u0010$R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/ml/tomatoscan/data/HistoryRepository;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "analysisDao", "Lcom/ml/tomatoscan/data/database/dao/AnalysisDao;", "database", "Lcom/ml/tomatoscan/data/database/TomatoScanDatabase;", "imageStorageHelper", "Lcom/ml/tomatoscan/data/ImageStorageHelper;", "cleanupOldAnalyses", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearHistory", "", "createImageUriFromByteArray", "", "byteArray", "", "([BLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteFromHistory", "scanResult", "Lcom/ml/tomatoscan/models/ScanResult;", "(Lcom/ml/tomatoscan/models/ScanResult;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAnalysisStatistics", "", "getHistory", "Lkotlinx/coroutines/flow/Flow;", "", "getImageBitmap", "Landroid/graphics/Bitmap;", "saveToHistory", "imageUri", "Landroid/net/Uri;", "bitmap", "(Lcom/ml/tomatoscan/models/ScanResult;Landroid/net/Uri;Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class HistoryRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.data.database.TomatoScanDatabase database = null;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.data.database.dao.AnalysisDao analysisDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.data.ImageStorageHelper imageStorageHelper = null;
    
    public HistoryRepository(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveToHistory(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.models.ScanResult scanResult, @org.jetbrains.annotations.Nullable()
    android.net.Uri imageUri, @org.jetbrains.annotations.Nullable()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<java.lang.Object> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.ml.tomatoscan.models.ScanResult>> getHistory() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteFromHistory(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.models.ScanResult scanResult, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAnalysisStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, ? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap getImageBitmap(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.models.ScanResult scanResult) {
        return null;
    }
    
    private final java.lang.Object cleanupOldAnalyses(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object createImageUriFromByteArray(byte[] byteArray, kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
}