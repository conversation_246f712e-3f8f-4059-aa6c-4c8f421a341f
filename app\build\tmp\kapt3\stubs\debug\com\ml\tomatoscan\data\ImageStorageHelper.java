package com.ml.tomatoscan.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0012\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J$\u0010\u000b\u001a\u0004\u0018\u00010\f2\u0006\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u0010J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u000e2\u0006\u0010\u0013\u001a\u00020\fJ\u0010\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u0010J\u000e\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aJ\u0006\u0010\u001b\u001a\u00020\u001cJ\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u000e2\u0006\u0010\u0019\u001a\u00020\u001aJ \u0010\u001e\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0010H\u0002J\u0018\u0010\u001f\u001a\u0004\u0018\u00010\u001a2\u0006\u0010 \u001a\u00020!H\u0086@\u00a2\u0006\u0002\u0010\"J,\u0010#\u001a\u0004\u0018\u00010\f2\u0006\u0010 \u001a\u00020!2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010$R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0005\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\b\u00a8\u0006%"}, d2 = {"Lcom/ml/tomatoscan/data/ImageStorageHelper;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "imageDirectory", "Ljava/io/File;", "getImageDirectory", "()Ljava/io/File;", "imageDirectory$delegate", "Lkotlin/Lazy;", "bitmapToByteArray", "", "bitmap", "Landroid/graphics/Bitmap;", "maxWidth", "", "maxHeight", "byteArrayToBitmap", "byteArray", "cleanupOldImages", "", "daysToKeep", "deleteImageFile", "", "filePath", "", "getStorageUsed", "", "loadImageFromInternalStorage", "resizeBitmap", "saveImageToInternalStorage", "uri", "Landroid/net/Uri;", "(Landroid/net/Uri;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uriToByteArray", "(Landroid/net/Uri;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class ImageStorageHelper {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy imageDirectory$delegate = null;
    
    public ImageStorageHelper(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    private final java.io.File getImageDirectory() {
        return null;
    }
    
    /**
     * Convert image URI to byte array for database storage
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object uriToByteArray(@org.jetbrains.annotations.NotNull()
    android.net.Uri uri, int maxWidth, int maxHeight, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super byte[]> $completion) {
        return null;
    }
    
    /**
     * Convert bitmap to byte array for database storage
     */
    @org.jetbrains.annotations.Nullable()
    public final byte[] bitmapToByteArray(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, int maxWidth, int maxHeight) {
        return null;
    }
    
    /**
     * Convert byte array back to bitmap for display
     */
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap byteArrayToBitmap(@org.jetbrains.annotations.NotNull()
    byte[] byteArray) {
        return null;
    }
    
    /**
     * Save image to internal storage and return file path
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveImageToInternalStorage(@org.jetbrains.annotations.NotNull()
    android.net.Uri uri, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Load bitmap from internal storage file path
     */
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap loadImageFromInternalStorage(@org.jetbrains.annotations.NotNull()
    java.lang.String filePath) {
        return null;
    }
    
    /**
     * Delete image file from internal storage
     */
    public final boolean deleteImageFile(@org.jetbrains.annotations.NotNull()
    java.lang.String filePath) {
        return false;
    }
    
    /**
     * Clean up old image files to free storage space
     */
    public final void cleanupOldImages(int daysToKeep) {
    }
    
    /**
     * Get total storage used by images
     */
    public final long getStorageUsed() {
        return 0L;
    }
    
    private final android.graphics.Bitmap resizeBitmap(android.graphics.Bitmap bitmap, int maxWidth, int maxHeight) {
        return null;
    }
}