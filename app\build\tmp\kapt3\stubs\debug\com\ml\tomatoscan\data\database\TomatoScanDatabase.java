package com.ml.tomatoscan.data.database;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u00052\u00020\u0001:\u0001\u0005B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&\u00a8\u0006\u0006"}, d2 = {"Lcom/ml/tomatoscan/data/database/TomatoScanDatabase;", "Landroidx/room/RoomDatabase;", "()V", "analysisDao", "Lcom/ml/tomatoscan/data/database/dao/AnalysisDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.ml.tomatoscan.data.database.entities.AnalysisEntity.class}, version = 1, exportSchema = false)
@androidx.room.TypeConverters(value = {com.ml.tomatoscan.data.database.converters.DateConverter.class, com.ml.tomatoscan.data.database.converters.StringListConverter.class})
public abstract class TomatoScanDatabase extends androidx.room.RoomDatabase {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.ml.tomatoscan.data.database.TomatoScanDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_1_2 = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.ml.tomatoscan.data.database.TomatoScanDatabase.Companion Companion = null;
    
    public TomatoScanDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.ml.tomatoscan.data.database.dao.AnalysisDao analysisDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u000bR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/ml/tomatoscan/data/database/TomatoScanDatabase$Companion;", "", "()V", "INSTANCE", "Lcom/ml/tomatoscan/data/database/TomatoScanDatabase;", "MIGRATION_1_2", "Landroidx/room/migration/Migration;", "destroyInstance", "", "getDatabase", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.ml.tomatoscan.data.database.TomatoScanDatabase getDatabase(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
        
        public final void destroyInstance() {
        }
    }
}