package com.ml.tomatoscan.data.database.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0018\u0010\u000f\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u0014\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00150\u0014H\'J\u001c\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00070\u00152\u0006\u0010\u0017\u001a\u00020\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0019J\u001c\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00070\u00152\u0006\u0010\u001b\u001a\u00020\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0019J\u0018\u0010\u001c\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u001d\u001a\u00020\u001eH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u001f\u001a\u0004\u0018\u00010 H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010!\u001a\b\u0012\u0004\u0012\u00020\"0\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00150\u00142\b\b\u0002\u0010$\u001a\u00020\u001eH\'J\u0014\u0010%\u001a\b\u0012\u0004\u0012\u00020&0\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\'\u001a\u00020\u00032\f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00070\u0015H\u00a7@\u00a2\u0006\u0002\u0010)J\u0016\u0010*\u001a\u00020\u000b2\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010+\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006,"}, d2 = {"Lcom/ml/tomatoscan/data/database/dao/AnalysisDao;", "", "deleteAllAnalyses", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAnalysis", "analysis", "Lcom/ml/tomatoscan/data/database/entities/AnalysisEntity;", "(Lcom/ml/tomatoscan/data/database/entities/AnalysisEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAnalysisById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldAnalyses", "cutoffDate", "findAnalysisByTimestamp", "timestamp", "Ljava/util/Date;", "(Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllAnalyses", "Lkotlinx/coroutines/flow/Flow;", "", "getAnalysesByDisease", "disease", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAnalysesBySeverity", "severity", "getAnalysisById", "getAnalysisCount", "", "getAverageConfidence", "", "getDiseaseStatistics", "Lcom/ml/tomatoscan/data/database/dao/DiseaseStatistic;", "getRecentAnalyses", "limit", "getSeverityStatistics", "Lcom/ml/tomatoscan/data/database/dao/SeverityStatistic;", "insertAnalyses", "analyses", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertAnalysis", "updateAnalysis", "app_debug"})
@androidx.room.Dao()
public abstract interface AnalysisDao {
    
    @androidx.room.Query(value = "SELECT * FROM analysis_results ORDER BY timestamp DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.ml.tomatoscan.data.database.entities.AnalysisEntity>> getAllAnalyses();
    
    @androidx.room.Query(value = "SELECT * FROM analysis_results ORDER BY timestamp DESC LIMIT :limit")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.ml.tomatoscan.data.database.entities.AnalysisEntity>> getRecentAnalyses(int limit);
    
    @androidx.room.Query(value = "SELECT * FROM analysis_results WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalysisById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.ml.tomatoscan.data.database.entities.AnalysisEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM analysis_results WHERE timestamp = :timestamp LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object findAnalysisByTimestamp(@org.jetbrains.annotations.NotNull()
    java.util.Date timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.ml.tomatoscan.data.database.entities.AnalysisEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM analysis_results WHERE diseaseDetected LIKE :disease ORDER BY timestamp DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalysesByDisease(@org.jetbrains.annotations.NotNull()
    java.lang.String disease, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.ml.tomatoscan.data.database.entities.AnalysisEntity>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM analysis_results WHERE severity = :severity ORDER BY timestamp DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalysesBySeverity(@org.jetbrains.annotations.NotNull()
    java.lang.String severity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.ml.tomatoscan.data.database.entities.AnalysisEntity>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM analysis_results")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalysisCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAnalysis(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.data.database.entities.AnalysisEntity analysis, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAnalyses(@org.jetbrains.annotations.NotNull()
    java.util.List<com.ml.tomatoscan.data.database.entities.AnalysisEntity> analyses, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAnalysis(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.data.database.entities.AnalysisEntity analysis, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAnalysis(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.data.database.entities.AnalysisEntity analysis, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM analysis_results WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAnalysisById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM analysis_results")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllAnalyses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM analysis_results WHERE timestamp < :cutoffDate")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldAnalyses(long cutoffDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT diseaseDetected, COUNT(*) as count FROM analysis_results GROUP BY diseaseDetected ORDER BY count DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDiseaseStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.ml.tomatoscan.data.database.dao.DiseaseStatistic>> $completion);
    
    @androidx.room.Query(value = "SELECT severity, COUNT(*) as count FROM analysis_results GROUP BY severity ORDER BY count DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSeverityStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.ml.tomatoscan.data.database.dao.SeverityStatistic>> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(confidence) as averageConfidence FROM analysis_results")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAverageConfidence(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Float> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}