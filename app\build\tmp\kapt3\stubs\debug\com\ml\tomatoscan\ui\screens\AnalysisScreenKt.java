package com.ml.tomatoscan.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000.\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0010\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0002H\u0003\u001a\u0010\u0010\u0007\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\tH\u0007\u001a4\u0010\n\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00050\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\u000eH\u0003\"\u001c\u0010\u0000\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0002\u0012\u0004\u0012\u00020\u00030\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"UriSaver", "Landroidx/compose/runtime/saveable/Saver;", "Landroid/net/Uri;", "", "AnalysisInProgressScreen", "", "uri", "AnalysisScreen", "viewModel", "Lcom/ml/tomatoscan/viewmodels/TomatoScanViewModel;", "ImagePreview", "fromCamera", "", "onAnalyze", "Lkotlin/Function0;", "onRetake", "app_debug"})
public final class AnalysisScreenKt {
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.runtime.saveable.Saver<android.net.Uri, java.lang.String> UriSaver = null;
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AnalysisScreen(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.TomatoScanViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ImagePreview(android.net.Uri uri, boolean fromCamera, kotlin.jvm.functions.Function0<kotlin.Unit> onAnalyze, kotlin.jvm.functions.Function0<kotlin.Unit> onRetake) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AnalysisInProgressScreen(android.net.Uri uri) {
    }
}