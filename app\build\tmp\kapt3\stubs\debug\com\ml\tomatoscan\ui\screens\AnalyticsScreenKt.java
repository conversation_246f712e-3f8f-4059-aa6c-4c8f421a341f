package com.ml.tomatoscan.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u0007\u001a\u0012\u0010\u0005\u001a\u00020\u00012\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u0016\u0010\b\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u0007\u001a6\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0011\u0010\u0012\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0013"}, d2 = {"AnalyticsChart", "", "scanHistory", "", "Lcom/ml/tomatoscan/models/ScanResult;", "AnalyticsScreen", "viewModel", "Lcom/ml/tomatoscan/viewmodels/TomatoScanViewModel;", "AnalyticsSummary", "SummaryCard", "title", "", "value", "modifier", "Landroidx/compose/ui/Modifier;", "color", "Landroidx/compose/ui/graphics/Color;", "SummaryCard-g2O1Hgs", "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/Modifier;J)V", "app_debug"})
public final class AnalyticsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AnalyticsScreen(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.TomatoScanViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AnalyticsSummary(@org.jetbrains.annotations.NotNull()
    java.util.List<com.ml.tomatoscan.models.ScanResult> scanHistory) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AnalyticsChart(@org.jetbrains.annotations.NotNull()
    java.util.List<com.ml.tomatoscan.models.ScanResult> scanHistory) {
    }
}