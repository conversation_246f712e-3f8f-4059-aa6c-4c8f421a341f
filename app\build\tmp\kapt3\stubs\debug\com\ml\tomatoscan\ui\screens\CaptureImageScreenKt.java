package com.ml.tomatoscan.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0014\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u00a8\u0006\u0006"}, d2 = {"CaptureImageScreen", "", "navController", "Landroidx/navigation/NavController;", "viewModel", "Lcom/ml/tomatoscan/viewmodels/TomatoScanViewModel;", "app_debug"})
public final class CaptureImageScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void CaptureImageScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.TomatoScanViewModel viewModel) {
    }
}