package com.ml.tomatoscan.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000P\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a$\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a2\u0010\u0005\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00072\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0018\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u000bH\u0007\u001a&\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000b2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0007\u001a\u0016\u0010\u0013\u001a\u00020\u00012\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001aF\u0010\u0015\u001a\u00020\u00012\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00070\u00102\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\b2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\b2\u0006\u0010\u0019\u001a\u00020\u001aH\u0007\u001a4\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00072\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u0010\u0019\u001a\u00020\u001aH\u0007\u001a\u0018\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020!H\u0007\u001a\b\u0010\"\u001a\u00020\u0001H\u0007\u001a\u001e\u0010#\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00072\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0010\u0010$\u001a\u00020\u00012\u0006\u0010%\u001a\u00020\u000bH\u0007\u001a\u0015\u0010&\u001a\u00020\'2\u0006\u0010%\u001a\u00020\u000bH\u0007\u00a2\u0006\u0002\u0010(\u00a8\u0006)"}, d2 = {"ClearAllConfirmationDialog", "", "onConfirm", "Lkotlin/Function0;", "onDismiss", "DeleteConfirmationDialog", "scanResult", "Lcom/ml/tomatoscan/models/ScanResult;", "Lkotlin/Function1;", "DetailRow", "label", "", "value", "DetailSection", "title", "items", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "EmptyHistoryState", "onStartScan", "HistoryContent", "scanHistory", "onItemClick", "onDeleteClick", "imageLoader", "Lcoil/ImageLoader;", "HistoryItem", "onClick", "HistoryScreen", "navController", "Landroidx/navigation/NavController;", "viewModel", "Lcom/ml/tomatoscan/viewmodels/TomatoScanViewModel;", "LoadingHistoryIndicator", "ScanResultDetailDialog", "SeverityChip", "severity", "getHistoryDiseaseColor", "Landroidx/compose/ui/graphics/Color;", "(Ljava/lang/String;)J", "app_debug"})
public final class HistoryScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void HistoryScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.TomatoScanViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LoadingHistoryIndicator() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EmptyHistoryState(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartScan) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void HistoryContent(@org.jetbrains.annotations.NotNull()
    java.util.List<com.ml.tomatoscan.models.ScanResult> scanHistory, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.ml.tomatoscan.models.ScanResult, kotlin.Unit> onItemClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.ml.tomatoscan.models.ScanResult, kotlin.Unit> onDeleteClick, @org.jetbrains.annotations.NotNull()
    coil.ImageLoader imageLoader) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void HistoryItem(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.models.ScanResult scanResult, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDeleteClick, @org.jetbrains.annotations.NotNull()
    coil.ImageLoader imageLoader) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void SeverityChip(@org.jetbrains.annotations.NotNull()
    java.lang.String severity) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ScanResultDetailDialog(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.models.ScanResult scanResult, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DetailRow(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DetailSection(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> items, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DeleteConfirmationDialog(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.models.ScanResult scanResult, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.ml.tomatoscan.models.ScanResult, kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ClearAllConfirmationDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getHistoryDiseaseColor(@org.jetbrains.annotations.NotNull()
    java.lang.String severity) {
        return 0L;
    }
}