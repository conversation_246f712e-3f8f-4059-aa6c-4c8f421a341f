package com.ml.tomatoscan.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0007\u001a \u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\nH\u0007\u001a&\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u0011H\u0007\u001a\b\u0010\u0012\u001a\u00020\u0001H\u0007\u00a8\u0006\u0013"}, d2 = {"BottomBar", "", "navController", "Landroidx/navigation/NavController;", "viewModel", "Lcom/ml/tomatoscan/viewmodels/TomatoScanViewModel;", "BottomNavGraph", "bottomNavController", "Landroidx/navigation/NavHostController;", "userViewModel", "Lcom/ml/tomatoscan/viewmodels/UserViewModel;", "EnhancedNavigationBarItem", "item", "Lcom/ml/tomatoscan/ui/navigation/BottomNavItem;", "isSelected", "", "onClick", "Lkotlin/Function0;", "MainScreen", "app_debug"})
public final class MainScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void MainScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BottomBar(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.TomatoScanViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EnhancedNavigationBarItem(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.ui.navigation.BottomNavItem item, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BottomNavGraph(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavHostController bottomNavController, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.TomatoScanViewModel viewModel, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.UserViewModel userViewModel) {
    }
}