package com.ml.tomatoscan.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a&\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u0006\u0010\u0007\u001a\u00020\bH\u0007\u00a8\u0006\t"}, d2 = {"RecentScansSection", "", "navController", "Landroidx/navigation/NavController;", "scanHistory", "", "Lcom/ml/tomatoscan/models/ScanResult;", "imageLoader", "Lcoil/ImageLoader;", "app_debug"})
public final class RecentScansScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void RecentScansSection(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.util.List<com.ml.tomatoscan.models.ScanResult> scanHistory, @org.jetbrains.annotations.NotNull()
    coil.ImageLoader imageLoader) {
    }
}