package com.ml.tomatoscan.ui.screens.analysis;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\u001a8\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\n\u0010\u000b\u001a\u0010\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000eH\u0007\u001a\u001c\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00032\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eH\u0007\u001a\u0015\u0010\u0011\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\u0003H\u0007\u00a2\u0006\u0002\u0010\u0013\u001a\u0015\u0010\u0014\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\u0003H\u0007\u00a2\u0006\u0002\u0010\u0013\u001a\u0010\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u0003H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0016"}, d2 = {"DetailSection", "", "title", "", "items", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "color", "Landroidx/compose/ui/graphics/Color;", "DetailSection-g2O1Hgs", "(Ljava/lang/String;Ljava/util/List;Landroidx/compose/ui/graphics/vector/ImageVector;J)V", "DiseaseAnalysisDetails", "scanResult", "Lcom/ml/tomatoscan/models/ScanResult;", "QualityDetails", "quality", "getDiseaseColor", "severity", "(Ljava/lang/String;)J", "getQualityColor", "getQualityIcon", "app_debug"})
public final class AnalysisDetailsKt {
    
    @androidx.compose.runtime.Composable()
    public static final void QualityDetails(@org.jetbrains.annotations.NotNull()
    java.lang.String quality, @org.jetbrains.annotations.Nullable()
    com.ml.tomatoscan.models.ScanResult scanResult) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DiseaseAnalysisDetails(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.models.ScanResult scanResult) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getDiseaseColor(@org.jetbrains.annotations.NotNull()
    java.lang.String severity) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getQualityColor(@org.jetbrains.annotations.NotNull()
    java.lang.String quality) {
        return 0L;
    }
    
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.graphics.vector.ImageVector getQualityIcon(@org.jetbrains.annotations.NotNull()
    java.lang.String quality) {
        return null;
    }
}