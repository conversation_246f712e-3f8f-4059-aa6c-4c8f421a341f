package com.ml.tomatoscan.ui.screens.analysis;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000>\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\u001a0\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a8\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\f0\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0012\u0010\u0013\u001a\u0012\u0010\u0014\u001a\u00020\u00012\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0017"}, d2 = {"AnalysisContent", "", "modifier", "Landroidx/compose/ui/Modifier;", "viewModel", "Lcom/ml/tomatoscan/viewmodels/TomatoScanViewModel;", "imageUri", "Landroid/net/Uri;", "onAnalyzeAnother", "Lkotlin/Function0;", "ResultSectionCard", "icon", "", "title", "items", "", "cardColor", "Landroidx/compose/ui/graphics/Color;", "ResultSectionCard-g2O1Hgs", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;J)V", "ScanResultDetails", "scanResult", "Lcom/ml/tomatoscan/models/ScanResult;", "app_debug"})
public final class AnalysisResultContentKt {
    
    @androidx.compose.runtime.Composable()
    public static final void AnalysisContent(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.viewmodels.TomatoScanViewModel viewModel, @org.jetbrains.annotations.NotNull()
    android.net.Uri imageUri, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAnalyzeAnother) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ScanResultDetails(@org.jetbrains.annotations.Nullable()
    com.ml.tomatoscan.models.ScanResult scanResult) {
    }
}