package com.ml.tomatoscan.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020\u0007J\u0006\u0010,\u001a\u00020(J\u0006\u0010-\u001a\u00020(J\b\u0010.\u001a\u00020/H\u0002J\u0010\u00100\u001a\u00020\u000e2\u0006\u0010+\u001a\u00020\u0007H\u0002J\u000e\u00101\u001a\u00020(2\u0006\u0010%\u001a\u00020\u000eJ\u0006\u00102\u001a\u00020(J\u0010\u00103\u001a\u00020(2\b\u00104\u001a\u0004\u0018\u00010\u0007J\u000e\u00105\u001a\u00020(2\u0006\u00106\u001a\u00020\tR\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\t0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u001b\u001a\u00020\u001c\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0017\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0012R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\t0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0012R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\t0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0012R\u001d\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0#0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0012R\u0019\u0010%\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0012\u00a8\u00067"}, d2 = {"Lcom/ml/tomatoscan/viewmodels/TomatoScanViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_analysisImageUri", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Landroid/net/Uri;", "_directCameraMode", "", "_isHistoryLoading", "_isLoading", "_isRefreshing", "_scanResult", "Lcom/ml/tomatoscan/models/ScanResult;", "analysisImageUri", "Lkotlinx/coroutines/flow/StateFlow;", "getAnalysisImageUri", "()Lkotlinx/coroutines/flow/StateFlow;", "directCameraMode", "getDirectCameraMode", "firebaseData", "Lcom/ml/tomatoscan/data/FirebaseData;", "geminiApi", "Lcom/ml/tomatoscan/data/GeminiApi;", "historyRepository", "Lcom/ml/tomatoscan/data/HistoryRepository;", "imageLoader", "Lcoil/ImageLoader;", "getImageLoader", "()Lcoil/ImageLoader;", "isHistoryLoading", "isLoading", "isRefreshing", "scanHistory", "", "getScanHistory", "scanResult", "getScanResult", "analyzeImage", "", "bitmap", "Landroid/graphics/Bitmap;", "imageUri", "clearAnalysisState", "clearHistory", "createMockAnalysisResult", "Lcom/ml/tomatoscan/data/TomatoAnalysisResult;", "createMockResult", "deleteFromHistory", "refresh", "setAnalysisImageUri", "uri", "setDirectCameraMode", "enabled", "app_debug"})
public final class TomatoScanViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.data.GeminiApi geminiApi = null;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.data.FirebaseData firebaseData = null;
    @org.jetbrains.annotations.NotNull()
    private final com.ml.tomatoscan.data.HistoryRepository historyRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final coil.ImageLoader imageLoader = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.ml.tomatoscan.models.ScanResult> _scanResult = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.ml.tomatoscan.models.ScanResult> scanResult = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isHistoryLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isHistoryLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.ml.tomatoscan.models.ScanResult>> scanHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isRefreshing = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRefreshing = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<android.net.Uri> _analysisImageUri = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<android.net.Uri> analysisImageUri = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _directCameraMode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> directCameraMode = null;
    
    public TomatoScanViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final coil.ImageLoader getImageLoader() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.ml.tomatoscan.models.ScanResult> getScanResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isHistoryLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.ml.tomatoscan.models.ScanResult>> getScanHistory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRefreshing() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<android.net.Uri> getAnalysisImageUri() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getDirectCameraMode() {
        return null;
    }
    
    public final void setAnalysisImageUri(@org.jetbrains.annotations.Nullable()
    android.net.Uri uri) {
    }
    
    public final void setDirectCameraMode(boolean enabled) {
    }
    
    public final void analyzeImage(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap bitmap, @org.jetbrains.annotations.NotNull()
    android.net.Uri imageUri) {
    }
    
    private final com.ml.tomatoscan.data.TomatoAnalysisResult createMockAnalysisResult() {
        return null;
    }
    
    private final com.ml.tomatoscan.models.ScanResult createMockResult(android.net.Uri imageUri) {
        return null;
    }
    
    public final void clearAnalysisState() {
    }
    
    public final void refresh() {
    }
    
    public final void deleteFromHistory(@org.jetbrains.annotations.NotNull()
    com.ml.tomatoscan.models.ScanResult scanResult) {
    }
    
    public final void clearHistory() {
    }
}